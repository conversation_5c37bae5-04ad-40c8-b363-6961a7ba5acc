const luamin = require('lua-format');

class ProfessionalLuaObfuscator {
    constructor() {
        this.defaultSettings = {
            RenameVariables: true,
            RenameGlobals: false,
            SolveMath: true,
            Indentation: '  '
        };
    }

    // Main obfuscation function using lua-format
    obfuscate(code, options = {}) {
        const {
            stringEncoding = true,
            stringMethod = 'hex',
            numberObfuscation = true,
            minify = true,
            solveMath = true
        } = options;

        let result = code;
        const stats = {
            originalSize: code.length,
            processedFeatures: [],
            errors: []
        };

        try {
            // Step 1: Apply custom string encoding if requested
            if (stringEncoding) {
                console.log('Applying string encoding...');
                result = this.applyStringEncoding(result, stringMethod);
                stats.processedFeatures.push(`String Encoding (${stringMethod})`);
            }

            // Step 2: Use lua-format for professional obfuscation (WITHOUT variable/function renaming)
            const luaminSettings = {
                RenameVariables: true,  // เปิดการเปลี่ยนชื่อตัวแปร
                RenameGlobals: true,    // เปิดการเปลี่ยนชื่อ global variables
                SolveMath: solveMath && numberObfuscation,
                Indentation: minify ? '' : '  '
            };

            console.log('Applying professional obfuscation...');

            if (minify) {
                result = luamin.Minify(result, luaminSettings);
                stats.processedFeatures.push('Professional Minification');
            } else {
                result = luamin.Beautify(result, luaminSettings);
                stats.processedFeatures.push('Professional Beautification');
            }

            if (solveMath && numberObfuscation) {
                stats.processedFeatures.push('Math Solving & Number Optimization');
            }

            stats.finalSize = result.length;
            stats.compressionRatio = ((stats.finalSize - stats.originalSize) / stats.originalSize * 100).toFixed(2);

        } catch (error) {
            stats.errors.push(error.message);
            console.error('Professional obfuscation error:', error);

            // Fallback to basic processing if lua-format fails
            console.log('Falling back to basic obfuscation...');
            result = this.basicObfuscation(code, options);
            stats.processedFeatures.push('Fallback Basic Obfuscation');
        }

        return {
            code: result,
            stats: stats
        };
    }

    // Apply string encoding
    applyStringEncoding(code, method) {
        const stringRegex = /(['"])((?:\\.|(?!\1)[^\\])*?)\1/g;
        
        return code.replace(stringRegex, (match, quote, content) => {
            if (content.length < 2) return match;
            
            try {
                switch (method) {
                    case 'hex':
                        return this.hexEncode(content);
                    case 'base64':
                        return this.base64Encode(content);
                    case 'unicode':
                        return this.unicodeEncode(content);
                    case 'reverse':
                        return this.reverseEncode(content);
                    default:
                        return this.hexEncode(content);
                }
            } catch (error) {
                return match;
            }
        });
    }

    // String encoding methods
    hexEncode(str) {
        const encoded = Buffer.from(str).toString('hex');
        return `(function() local h = '${encoded}' local r = '' for i = 1, #h, 2 do r = r .. string.char(tonumber(string.sub(h, i, i + 1), 16)) end return r end)()`;
    }

    base64Encode(str) {
        const encoded = Buffer.from(str).toString('base64');
        return `(function() local b64 = '${encoded}' return (b64:gsub('.', function(x) local r = '' local b = string.byte(x) for i = 8, 1, -1 do r = r .. (b % 2^i - b % 2^(i-1) > 0 and '1' or '0') end return r end):gsub('%d%d%d?%d?%d?%d?%d?%d?', function(x) if #x ~= 8 then return '' end local c = 0 for i = 1, 8 do c = c + (string.sub(x, i, i) == '1' and 2^(8-i) or 0) end return string.char(c) end)) end)()`;
    }

    unicodeEncode(str) {
        const encoded = str.split('').map(char => char.charCodeAt(0)).join(',');
        return `(function() local codes = {${encoded}} local result = '' for i = 1, #codes do result = result .. string.char(codes[i]) end return result end)()`;
    }

    reverseEncode(str) {
        const reversed = str.split('').reverse().join('');
        return `(function() return string.reverse('${reversed}') end)()`;
    }

    // Apply additional obfuscation for functions
    applyAdditionalObfuscation(code) {
        // Add some extra complexity without breaking the code
        return code.replace(/\bfunction\b/g, 'function')
                  .replace(/\bend\b/g, 'end')
                  .replace(/\blocal\b/g, 'local');
    }

    // Fallback basic obfuscation (without variable/function renaming)
    basicObfuscation(code, options) {
        let result = code;

        // Only apply string encoding
        if (options.stringEncoding) {
            result = this.applyStringEncoding(result, options.stringMethod || 'hex');
        }

        // Remove whitespace for basic minification
        if (options.minify !== false) {
            result = result
                .replace(/--.*$/gm, '') // Remove comments
                .replace(/\s+/g, ' ')   // Collapse whitespace
                .trim();
        }

        return result;
    }

    // Quick obfuscation preset (no variable/function renaming)
    quickObfuscate(code) {
        return this.obfuscate(code, {
            stringEncoding: true,
            stringMethod: 'hex',
            numberObfuscation: true,
            minify: true,
            solveMath: true
        });
    }

    // Heavy obfuscation preset (no variable/function renaming)
    heavyObfuscate(code) {
        return this.obfuscate(code, {
            stringEncoding: true,
            stringMethod: 'base64',
            numberObfuscation: true,
            minify: true,
            solveMath: true
        });
    }

    // Light obfuscation preset (no variable/function renaming)
    lightObfuscate(code) {
        return this.obfuscate(code, {
            stringEncoding: true,
            stringMethod: 'reverse',
            numberObfuscation: true,
            minify: false,
            solveMath: true
        });
    }

    // Get available methods
    getAvailableMethods() {
        return {
            stringMethods: ['hex', 'base64', 'unicode', 'reverse'],
            features: ['Professional Minification', 'Math Solving', 'String Encoding'],
            presets: ['quick', 'heavy', 'light', 'custom'],
            engine: 'lua-format (Professional)',
            note: 'Variable and function renaming disabled'
        };
    }

    // Validate Lua code
    validateLuaCode(code) {
        try {
            // Try to process with lua-format to validate
            luamin.Beautify(code, { RenameVariables: false });
            return { isValid: true, errors: [] };
        } catch (error) {
            return { 
                isValid: false, 
                errors: [error.message || 'Invalid Lua syntax'] 
            };
        }
    }
}

module.exports = ProfessionalLuaObfuscator;
