// Test API without variable/function renaming
const testCode = `local function myFunction(playerName)
    print("Welcome " .. playerName)
    local playerScore = 100
    return playerScore
end

local myPlayer = "TestPlayer"
local finalScore = myFunction(myPlayer)
print("Score: " .. finalScore)`;

async function testAPINoRenaming() {
    console.log('🧪 Testing API WITHOUT Variable/Function Renaming\n');

    try {
        // Test quick obfuscation
        console.log('1. Testing Quick Obfuscation...');
        const quickResponse = await fetch('http://localhost:3000/api/obfuscate/quick', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ code: testCode })
        });
        
        const quickData = await quickResponse.json();
        if (quickData.success) {
            console.log('✅ Quick obfuscation successful');
            console.log('Features:', quickData.data.stats.processedFeatures.join(', '));
            console.log('Size change:', quickData.data.stats.compressionRatio + '%');
            console.log('Engine:', quickData.data.stats.engine || 'Professional');
            console.log('Note:', quickData.data.stats.note || 'Variable/function renaming disabled');
            console.log('');
            
            console.log('Obfuscated Code Preview:');
            console.log('─'.repeat(50));
            console.log(quickData.data.obfuscatedCode.substring(0, 300) + '...');
            console.log('─'.repeat(50));
            console.log('');
        } else {
            console.log('❌ Quick obfuscation failed:', quickData.message);
        }

        // Test methods endpoint
        console.log('2. Testing Methods Endpoint...');
        const methodsResponse = await fetch('http://localhost:3000/api/methods');
        const methodsData = await methodsResponse.json();
        if (methodsData.success) {
            console.log('✅ Methods endpoint successful');
            console.log('Engine:', methodsData.data.engine);
            console.log('String methods:', methodsData.data.stringMethods.join(', '));
            console.log('Features:', methodsData.data.features ? methodsData.data.features.join(', ') : 'N/A');
            console.log('Note:', methodsData.data.note);
            console.log('');
        } else {
            console.log('❌ Methods endpoint failed');
        }

        console.log('🎉 API tests completed successfully!');
        console.log('✅ Variable and function names are preserved');
        console.log('🔒 Only string encoding and math optimization applied');

    } catch (error) {
        console.error('❌ Test failed:', error.message);
    }
}

testAPINoRenaming();
