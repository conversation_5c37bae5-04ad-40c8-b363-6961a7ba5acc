// Global variables
let inputEditor, outputEditor;

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeEditors();
    loadExampleCode();
});

// Initialize CodeMirror editors
function initializeEditors() {
    // Input editor
    inputEditor = CodeMirror.fromTextArea(document.getElementById('inputCode'), {
        mode: 'lua',
        theme: 'monokai',
        lineNumbers: true,
        indentUnit: 4,
        lineWrapping: true,
        autoCloseBrackets: true,
        matchBrackets: true,
        foldGutter: true,
        gutters: ['CodeMirror-linenumbers', 'CodeMirror-foldgutter']
    });

    // Output editor
    outputEditor = CodeMirror.fromTextArea(document.getElementById('outputCode'), {
        mode: 'lua',
        theme: 'monokai',
        lineNumbers: true,
        readOnly: true,
        lineWrapping: true,
        foldGutter: true,
        gutters: ['CodeMirror-linenumbers', 'CodeMirror-foldgutter']
    });

    // Set editor heights
    inputEditor.setSize(null, 400);
    outputEditor.setSize(null, 400);
}

// Load example code
function loadExampleCode() {
    const exampleCode = `-- Roblox Script Example
local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local UserInputService = game:GetService("UserInputService")

local player = Players.LocalPlayer
local playerGui = player:WaitForChild("PlayerGui")

local function createWelcomeMessage()
    local screenGui = Instance.new("ScreenGui")
    screenGui.Name = "WelcomeGui"
    screenGui.Parent = playerGui
    
    local frame = Instance.new("Frame")
    frame.Size = UDim2.new(0, 300, 0, 100)
    frame.Position = UDim2.new(0.5, -150, 0.5, -50)
    frame.BackgroundColor3 = Color3.fromRGB(50, 50, 50)
    frame.BorderSizePixel = 0
    frame.Parent = screenGui
    
    local textLabel = Instance.new("TextLabel")
    textLabel.Size = UDim2.new(1, 0, 1, 0)
    textLabel.BackgroundTransparency = 1
    textLabel.Text = "Welcome, " .. player.Name .. "!"
    textLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
    textLabel.TextScaled = true
    textLabel.Font = Enum.Font.SourceSansBold
    textLabel.Parent = frame
    
    return screenGui
end

local function onPlayerJoined()
    wait(2)
    local welcomeGui = createWelcomeMessage()
    
    wait(5)
    welcomeGui:Destroy()
end

-- Initialize
onPlayerJoined()`;

    if (inputEditor) {
        inputEditor.setValue(exampleCode);
    }
}

// Toggle custom options panel
function toggleCustomOptions() {
    const panel = document.getElementById('customOptions');
    const isVisible = panel.style.display !== 'none';
    panel.style.display = isVisible ? 'none' : 'block';
}

// Main obfuscation function
async function obfuscateCode(preset) {
    const code = inputEditor.getValue().trim();
    
    if (!code) {
        showError('Please enter some Lua code to obfuscate.');
        return;
    }

    showLoading(true);
    hideError();
    hideStats();

    try {
        let endpoint = '/api/obfuscate';
        let requestBody = { code };

        // Set endpoint based on preset
        if (preset !== 'custom') {
            endpoint = `/api/obfuscate/${preset}`;
        } else {
            // Get custom options
            const options = getCustomOptions();
            requestBody.options = options;
        }

        const response = await fetch(endpoint, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestBody)
        });

        const result = await response.json();

        if (result.success) {
            outputEditor.setValue(result.data.obfuscatedCode);
            showStats(result.data.stats);
            showSuccess(`Code obfuscated successfully using ${preset} preset!`);
        } else {
            showError(result.message || 'Obfuscation failed');
        }

    } catch (error) {
        console.error('Obfuscation error:', error);
        showError('Network error: Unable to connect to the obfuscation service.');
    } finally {
        showLoading(false);
    }
}

// Get custom options from form
function getCustomOptions() {
    return {
        stringEncoding: document.getElementById('stringEncoding').checked,
        stringMethod: document.getElementById('stringMethod').value,
        numberObfuscation: document.getElementById('numberObfuscation').checked,
        numberMethod: document.getElementById('numberMethod').value,
        mixedNumberMethods: document.getElementById('mixedNumberMethods').checked
        // Variable and function renaming removed
    };
}

// Show loading indicator
function showLoading(show) {
    const indicator = document.getElementById('loadingIndicator');
    indicator.style.display = show ? 'flex' : 'none';
}

// Show error message
function showError(message) {
    const errorDisplay = document.getElementById('errorDisplay');
    const errorMessage = document.getElementById('errorMessage');
    errorMessage.textContent = message;
    errorDisplay.style.display = 'block';
    
    // Auto-hide after 5 seconds
    setTimeout(() => {
        hideError();
    }, 5000);
}

// Hide error message
function hideError() {
    const errorDisplay = document.getElementById('errorDisplay');
    errorDisplay.style.display = 'none';
}

// Show success message
function showSuccess(message) {
    // Remove existing success messages
    const existingSuccess = document.querySelector('.success-display');
    if (existingSuccess) {
        existingSuccess.remove();
    }

    // Create success message
    const successDiv = document.createElement('div');
    successDiv.className = 'success-display';
    successDiv.innerHTML = `
        <div class="success-content">
            <i class="fas fa-check-circle"></i>
            <span>${message}</span>
        </div>
    `;

    // Insert after control panel
    const controlPanel = document.querySelector('.control-panel');
    controlPanel.parentNode.insertBefore(successDiv, controlPanel.nextSibling);

    // Auto-hide after 3 seconds
    setTimeout(() => {
        successDiv.remove();
    }, 3000);
}

// Show statistics
function showStats(stats) {
    const statsPanel = document.getElementById('statsPanel');
    
    document.getElementById('originalSize').textContent = formatBytes(stats.originalSize);
    document.getElementById('finalSize').textContent = formatBytes(stats.finalSize);
    document.getElementById('sizeChange').textContent = `${stats.compressionRatio}%`;
    document.getElementById('featuresApplied').textContent = stats.processedFeatures.join(', ');
    
    // Hide variable mapping since renaming is disabled
    document.getElementById('variableMap').style.display = 'none';
    
    statsPanel.style.display = 'block';
}

// Hide statistics
function hideStats() {
    const statsPanel = document.getElementById('statsPanel');
    statsPanel.style.display = 'none';
}

// Format bytes to human readable format
function formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Clear input
function clearInput() {
    inputEditor.setValue('');
    outputEditor.setValue('');
    hideStats();
    hideError();
}

// Load example
function loadExample() {
    loadExampleCode();
    hideStats();
    hideError();
}

// Copy output to clipboard
async function copyOutput() {
    const code = outputEditor.getValue();
    if (!code.trim()) {
        showError('No obfuscated code to copy.');
        return;
    }

    try {
        await navigator.clipboard.writeText(code);
        showSuccess('Obfuscated code copied to clipboard!');
    } catch (error) {
        console.error('Copy failed:', error);
        showError('Failed to copy to clipboard. Please select and copy manually.');
    }
}

// Download output as file
function downloadOutput() {
    const code = outputEditor.getValue();
    if (!code.trim()) {
        showError('No obfuscated code to download.');
        return;
    }

    const blob = new Blob([code], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'obfuscated_script.lua';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    showSuccess('Obfuscated code downloaded successfully!');
}

// Show info modal
function showInfo() {
    alert(`Lua Obfuscator v1.0.0

This tool provides professional-grade obfuscation for Lua scripts, specifically designed for Roblox development.

Features:
• String Encoding (6 methods)
• Variable Renaming
• Number Obfuscation (6 methods)
• Multiple preset configurations
• Real-time statistics

Created for protecting your Roblox scripts from reverse engineering.`);
}

// Show help modal
function showHelp() {
    alert(`How to use Lua Obfuscator:

1. Paste your Lua/Roblox script in the left editor
2. Choose an obfuscation preset:
   • Quick: Fast obfuscation with basic protection
   • Heavy: Maximum protection with all features
   • Light: Minimal obfuscation for testing
   • Custom: Configure your own settings

3. Click the obfuscation button
4. Copy or download the obfuscated code

Tips:
• Test your obfuscated code before deployment
• Heavy obfuscation may increase script size
• Keep backups of your original code`);
}
