class NumberObfuscator {
    constructor() {
        this.obfuscationMethods = {
            arithmetic: this.arithmeticObfuscation.bind(this),
            bitwise: this.bitwiseObfuscation.bind(this),
            hex: this.hexObfuscation.bind(this),
            expression: this.expressionObfuscation.bind(this),
            table: this.tableObfuscation.bind(this),
            function: this.functionObfuscation.bind(this)
        };
    }

    // Arithmetic obfuscation (addition, subtraction, multiplication, division)
    arithmeticObfuscation(num) {
        const operations = [
            () => {
                const a = Math.floor(Math.random() * 100) + 1;
                const b = num + a;
                return `(${b} - ${a})`;
            },
            () => {
                const a = Math.floor(Math.random() * 100) + 1;
                const b = num - a;
                return `(${b} + ${a})`;
            },
            () => {
                if (num !== 0) {
                    const a = Math.floor(Math.random() * 10) + 2;
                    const b = num * a;
                    return `(${b} / ${a})`;
                }
                return num.toString();
            },
            () => {
                const a = Math.floor(Math.random() * 10) + 2;
                const b = Math.floor(num / a);
                const remainder = num % a;
                return remainder === 0 ? `(${a} * ${b})` : `(${a} * ${b} + ${remainder})`;
            }
        ];

        return operations[Math.floor(Math.random() * operations.length)]();
    }

    // Bitwise obfuscation
    bitwiseObfuscation(num) {
        if (num < 0 || num > 0xFFFFFFFF) return num.toString();
        
        const operations = [
            () => {
                const mask = Math.floor(Math.random() * 0xFF) + 1;
                return `(${num ^ mask} ~ ${mask})`;
            },
            () => {
                const shift = Math.floor(Math.random() * 4) + 1;
                const shifted = num << shift;
                return `(${shifted} >> ${shift})`;
            },
            () => {
                const shift = Math.floor(Math.random() * 4) + 1;
                const shifted = num >> shift;
                return `(${shifted} << ${shift})`;
            }
        ];

        return operations[Math.floor(Math.random() * operations.length)]();
    }

    // Hexadecimal obfuscation
    hexObfuscation(num) {
        return `0x${num.toString(16).toUpperCase()}`;
    }

    // Complex expression obfuscation
    expressionObfuscation(num) {
        const expressions = [
            () => {
                const a = Math.floor(Math.random() * 50) + 1;
                const b = Math.floor(Math.random() * 50) + 1;
                const c = num - a - b;
                return `(${a} + ${b} + ${c})`;
            },
            () => {
                const a = Math.floor(Math.random() * 10) + 1;
                const b = Math.floor(Math.random() * 10) + 1;
                const c = num - (a * b);
                return `(${a} * ${b} + ${c})`;
            },
            () => {
                if (num > 10) {
                    const a = Math.floor(Math.random() * Math.floor(num / 2)) + 1;
                    const b = num - a;
                    return `(${a} + ${b})`;
                }
                return num.toString();
            }
        ];

        return expressions[Math.floor(Math.random() * expressions.length)]();
    }

    // Table-based obfuscation
    tableObfuscation(num) {
        const tableSize = Math.floor(Math.random() * 5) + 3;
        const index = Math.floor(Math.random() * tableSize);
        const table = [];
        
        for (let i = 0; i < tableSize; i++) {
            if (i === index) {
                table.push(num);
            } else {
                table.push(Math.floor(Math.random() * 1000));
            }
        }
        
        return `({${table.join(', ')}})[${index + 1}]`;
    }

    // Function-based obfuscation
    functionObfuscation(num) {
        const methods = [
            () => `(function() return ${num} end)()`,
            () => {
                const a = Math.floor(Math.random() * 100) + 1;
                const b = num + a;
                return `(function(x) return x - ${a} end)(${b})`;
            },
            () => {
                const a = Math.floor(Math.random() * 10) + 2;
                const b = num * a;
                return `(function(x) return x / ${a} end)(${b})`;
            }
        ];

        return methods[Math.floor(Math.random() * methods.length)]();
    }

    // Main obfuscation function
    obfuscateNumber(num, method = 'arithmetic') {
        if (!this.obfuscationMethods[method]) {
            throw new Error(`Unknown obfuscation method: ${method}`);
        }
        
        // Skip very small numbers or special cases
        if (num === 0 || num === 1 || num === -1) {
            return num.toString();
        }
        
        return this.obfuscationMethods[method](num);
    }

    // Process Lua code to obfuscate all numbers
    processLuaCode(code, method = 'arithmetic') {
        // Match number literals (integers and floats)
        const numberRegex = /\b(\d+(?:\.\d+)?)\b/g;
        
        return code.replace(numberRegex, (match) => {
            const num = parseFloat(match);
            
            // Skip numbers that are part of strings or comments
            if (isNaN(num)) return match;
            
            // Randomly choose whether to obfuscate this number (70% chance)
            if (Math.random() < 0.7) {
                try {
                    return this.obfuscateNumber(num, method);
                } catch (error) {
                    console.warn(`Failed to obfuscate number: ${num}`, error);
                    return match;
                }
            }
            
            return match;
        });
    }

    // Mixed obfuscation using random methods
    processLuaCodeMixed(code) {
        const methods = Object.keys(this.obfuscationMethods);
        const numberRegex = /\b(\d+(?:\.\d+)?)\b/g;
        
        return code.replace(numberRegex, (match) => {
            const num = parseFloat(match);
            
            if (isNaN(num)) return match;
            
            // Randomly choose whether to obfuscate (70% chance)
            if (Math.random() < 0.7) {
                const randomMethod = methods[Math.floor(Math.random() * methods.length)];
                try {
                    return this.obfuscateNumber(num, randomMethod);
                } catch (error) {
                    console.warn(`Failed to obfuscate number: ${num}`, error);
                    return match;
                }
            }
            
            return match;
        });
    }
}

module.exports = NumberObfuscator;
