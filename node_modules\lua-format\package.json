{"name": "lua-format", "version": "1.5.2", "description": "Lua Beautifier & Minifier, written in JavaScript.\r", "main": "src/index.js", "scripts": {"start": "node ./src/beautify.js", "test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://github.com/Herrtt/luamin.js.git"}, "keywords": ["luamin", "lua-format", "prettifier", "beautifier", "uglifier", "minfier", "lua", "formatter"], "author": "<PERSON><PERSON>", "license": "ISC", "bugs": {"url": "https://github.com/Herrtt/luamin.js/issues"}, "homepage": "https://github.com/Herrtt/luamin.js#readme"}