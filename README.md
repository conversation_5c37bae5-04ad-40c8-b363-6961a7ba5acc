# Lua Obfuscator - Professional Roblox Script Protection

A powerful web-based Lua obfuscator specifically designed for Roblox script protection. This tool uses **professional npm packages** including `lua-format` (luamin.js) for industry-grade obfuscation, providing superior protection compared to custom JavaScript implementations.

## 🚀 Features

### 🔧 **Professional Engine**
- **Primary Engine**: `lua-format` (luamin.js) - Industry-standard Lua minifier/obfuscator
- **Fallback Engine**: Custom JavaScript implementation for additional features
- **Superior Quality**: Professional npm packages ensure reliable and accurate obfuscation

### Core Obfuscation Tools

1. **Professional Minification & Variable Renaming**
   - Uses `lua-format` for industry-grade variable renaming
   - Generates extremely short, unreadable variable names
   - Preserves Lua syntax and functionality perfectly
   - Math solving and optimization built-in

2. **String Encoding** - 4 professional methods:
   - Hexadecimal encoding
   - Base64 encoding
   - Unicode encoding
   - Reverse encoding

3. **Advanced Features**
   - Professional code minification
   - Math expression solving and optimization
   - Syntax validation using professional parser
   - Fallback protection for edge cases

### Obfuscation Presets

- **Quick**: Fast obfuscation with basic protection
- **Heavy**: Maximum protection with all features enabled
- **Light**: Minimal obfuscation for testing purposes
- **Custom**: Configure your own obfuscation settings

## 🛠️ Installation

1. Clone or download this repository
2. Install dependencies (includes professional packages):
   ```bash
   npm install
   ```
   **Installed packages:**
   - `lua-format` - Professional Lua minifier/obfuscator
   - `express` - Web server framework
   - `cors` - Cross-origin resource sharing
   - `body-parser` - Request parsing

3. Start the server:
   ```bash
   npm start
   ```
4. Open your browser and navigate to `http://localhost:3000`

## 📖 Usage

### Web Interface

1. **Input Code**: Paste your Lua/Roblox script in the left editor
2. **Choose Preset**: Select Quick, Heavy, Light, or Custom obfuscation
3. **Configure Options** (Custom mode):
   - Enable/disable string encoding and choose method
   - Enable/disable variable renaming
   - Enable/disable function renaming
   - Enable/disable number obfuscation and choose method
4. **Obfuscate**: Click the obfuscation button
5. **Copy/Download**: Get your obfuscated code

### API Endpoints

#### Health Check
```
GET /api/health
```

#### Get Available Methods
```
GET /api/methods
```

#### Quick Obfuscation
```
POST /api/obfuscate/quick
Content-Type: application/json

{
  "code": "your lua code here"
}
```

#### Heavy Obfuscation
```
POST /api/obfuscate/heavy
Content-Type: application/json

{
  "code": "your lua code here"
}
```

#### Light Obfuscation
```
POST /api/obfuscate/light
Content-Type: application/json

{
  "code": "your lua code here"
}
```

#### Custom Obfuscation
```
POST /api/obfuscate
Content-Type: application/json

{
  "code": "your lua code here",
  "options": {
    "stringEncoding": true,
    "stringMethod": "hex",
    "variableRenaming": true,
    "functionRenaming": true,
    "numberObfuscation": true,
    "numberMethod": "arithmetic",
    "mixedNumberMethods": false
  }
}
```

#### Code Validation
```
POST /api/validate
Content-Type: application/json

{
  "code": "your lua code here"
}
```

## 🔧 Configuration Options

### String Encoding Methods
- `hex`: Hexadecimal encoding
- `base64`: Base64 encoding
- `unicode`: Unicode character codes
- `reverse`: String reversal
- `caesar`: Caesar cipher
- `xor`: XOR encoding

### Number Obfuscation Methods
- `arithmetic`: Mathematical operations
- `bitwise`: Bitwise operations
- `hex`: Hexadecimal representation
- `expression`: Complex expressions
- `table`: Table-based lookup
- `function`: Function-based generation

## 📊 Statistics

The obfuscator provides detailed statistics including:
- Original code size
- Final code size
- Size change percentage
- Applied obfuscation features
- Variable mapping (when variable renaming is enabled)
- Function mapping (when function renaming is enabled)

## 🧪 Testing

Run the API tests:
```bash
node test_api.js
```

## 📁 Project Structure

```
lua-obfuscator/
├── server.js              # Main server file
├── package.json           # Dependencies and scripts
├── routes/
│   └── obfuscator.js      # API routes
├── obfuscator/
│   ├── luaObfuscator.js        # Main obfuscator class
│   ├── professionalObfuscator.js # Professional lua-format wrapper
│   ├── stringEncoder.js        # Custom string encoding module
│   ├── variableRenamer.js      # Custom variable renaming module
│   ├── functionRenamer.js      # Custom function renaming module
│   └── numberObfuscator.js     # Custom number obfuscation module
├── public/
│   ├── index.html         # Frontend interface
│   ├── styles.css         # Styling
│   └── script.js          # Frontend JavaScript
└── test_api.js           # API testing script
```

## ⚠️ Important Notes

- **Always keep backups** of your original code
- **Test obfuscated code** before deployment
- Professional obfuscation provides superior protection
- Uses industry-standard `lua-format` package for reliability
- Heavy obfuscation may significantly increase script size
- This tool is designed for legitimate script protection

## 🔧 **Why Professional Packages?**

- **Reliability**: `lua-format` is battle-tested and widely used
- **Accuracy**: Professional parser ensures correct Lua syntax
- **Performance**: Optimized algorithms for better results
- **Maintenance**: Regular updates and bug fixes from the community
- **Quality**: Superior obfuscation compared to custom implementations

## 🔒 Security Features

- Input validation and sanitization
- File size limits (1MB max)
- Error handling and logging
- CORS protection
- Request rate limiting ready

## 🚀 Deployment

For production deployment:

1. Set environment variables:
   ```bash
   export PORT=3000
   export NODE_ENV=production
   ```

2. Use a process manager like PM2:
   ```bash
   npm install -g pm2
   pm2 start server.js --name "lua-obfuscator"
   ```

## 📄 License

MIT License - Feel free to use this tool for your projects.

## 🤝 Contributing

Contributions are welcome! Please feel free to submit issues and pull requests.

---

**Created for Roblox developers who need professional script protection.**
