class VariableRenamer {
    constructor() {
        this.reservedWords = new Set([
            'and', 'break', 'do', 'else', 'elseif', 'end', 'false', 'for',
            'function', 'if', 'in', 'local', 'nil', 'not', 'or', 'repeat',
            'return', 'then', 'true', 'until', 'while', 'goto'
        ]);
        
        this.builtinFunctions = new Set([
            'print', 'pairs', 'ipairs', 'next', 'type', 'getmetatable', 'setmetatable',
            'rawget', 'rawset', 'rawequal', 'rawlen', 'tostring', 'tonumber',
            'pcall', 'xpcall', 'error', 'assert', 'select', 'unpack', 'table',
            'string', 'math', 'io', 'os', 'debug', 'coroutine', 'package',
            'require', 'load', 'loadfile', 'loadstring', 'dofile'
        ]);
        
        this.variableMap = new Map();
        this.usedNames = new Set();
    }

    // Generate random variable names
    generateRandomName(length = 8) {
        const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        const numbers = '0123456789';
        let result = '';
        
        // First character must be a letter
        result += chars.charAt(Math.floor(Math.random() * chars.length));
        
        // Rest can be letters or numbers
        const allChars = chars + numbers;
        for (let i = 1; i < length; i++) {
            result += allChars.charAt(Math.floor(Math.random() * allChars.length));
        }
        
        return result;
    }

    // Generate obfuscated variable names
    generateObfuscatedName(original) {
        if (this.variableMap.has(original)) {
            return this.variableMap.get(original);
        }

        let newName;
        do {
            newName = this.generateRandomName();
        } while (
            this.usedNames.has(newName) || 
            this.reservedWords.has(newName) || 
            this.builtinFunctions.has(newName)
        );

        this.variableMap.set(original, newName);
        this.usedNames.add(newName);
        return newName;
    }

    // Find all variable declarations and usages
    findVariables(code) {
        const variables = new Set();
        
        // Match local variable declarations
        const localVarRegex = /local\s+([a-zA-Z_][a-zA-Z0-9_]*(?:\s*,\s*[a-zA-Z_][a-zA-Z0-9_]*)*)/g;
        let match;
        
        while ((match = localVarRegex.exec(code)) !== null) {
            const varList = match[1].split(',').map(v => v.trim());
            varList.forEach(varName => {
                if (!this.reservedWords.has(varName) && !this.builtinFunctions.has(varName)) {
                    variables.add(varName);
                }
            });
        }

        // Match function parameters
        const functionRegex = /function\s*(?:[a-zA-Z_][a-zA-Z0-9_]*\s*)?\(([^)]*)\)/g;
        while ((match = functionRegex.exec(code)) !== null) {
            if (match[1].trim()) {
                const params = match[1].split(',').map(p => p.trim());
                params.forEach(param => {
                    if (param && !this.reservedWords.has(param) && !this.builtinFunctions.has(param)) {
                        variables.add(param);
                    }
                });
            }
        }

        // Match for loop variables
        const forRegex = /for\s+([a-zA-Z_][a-zA-Z0-9_]*(?:\s*,\s*[a-zA-Z_][a-zA-Z0-9_]*)*)\s+in/g;
        while ((match = forRegex.exec(code)) !== null) {
            const varList = match[1].split(',').map(v => v.trim());
            varList.forEach(varName => {
                if (!this.reservedWords.has(varName) && !this.builtinFunctions.has(varName)) {
                    variables.add(varName);
                }
            });
        }

        return Array.from(variables);
    }

    // Replace variables in code
    replaceVariables(code, variables) {
        let result = code;
        
        // Sort variables by length (longest first) to avoid partial replacements
        const sortedVars = variables.sort((a, b) => b.length - a.length);
        
        sortedVars.forEach(varName => {
            const newName = this.generateObfuscatedName(varName);
            
            // Create regex to match whole words only
            const regex = new RegExp(`\\b${varName.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b`, 'g');
            result = result.replace(regex, newName);
        });
        
        return result;
    }

    // Main function to rename variables
    renameVariables(code) {
        this.variableMap.clear();
        this.usedNames.clear();
        
        const variables = this.findVariables(code);
        return this.replaceVariables(code, variables);
    }

    // Get the variable mapping for debugging
    getVariableMap() {
        return Object.fromEntries(this.variableMap);
    }
}

module.exports = VariableRenamer;
