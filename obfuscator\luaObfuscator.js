const StringEncoder = require('./stringEncoder');
const VariableRenamer = require('./variableRenamer');
const FunctionRenamer = require('./functionRenamer');
const NumberObfuscator = require('./numberObfuscator');
const ProfessionalLuaObfuscator = require('./professionalObfuscator');

class LuaObfuscator {
    constructor() {
        this.stringEncoder = new StringEncoder();
        this.variableRenamer = new VariableRenamer();
        this.functionRenamer = new FunctionRenamer();
        this.numberObfuscator = new NumberObfuscator();
        this.professionalObfuscator = new ProfessionalLuaObfuscator();
        this.useProfessional = true; // Use professional obfuscator by default
    }

    // Main obfuscation function
    obfuscate(code, options = {}) {
        // Use professional obfuscator if enabled
        if (this.useProfessional) {
            console.log('Using professional lua-format obfuscator...');
            return this.professionalObfuscator.obfuscate(code, options);
        }

        // Fallback to custom obfuscator
        const {
            stringEncoding = true,
            stringMethod = 'hex',
            numberObfuscation = true,
            numberMethod = 'arithmetic',
            mixedNumberMethods = false
        } = options;

        let result = code;
        const stats = {
            originalSize: code.length,
            processedFeatures: [],
            variableMap: {},
            functionMap: {},
            errors: []
        };

        try {
            // Step 1: String encoding
            if (stringEncoding) {
                console.log('Applying string encoding...');
                result = this.stringEncoder.processLuaCode(result, stringMethod);
                stats.processedFeatures.push(`String Encoding (${stringMethod})`);
            }

            // Step 2: Number obfuscation
            if (numberObfuscation) {
                console.log('Applying number obfuscation...');
                if (mixedNumberMethods) {
                    result = this.numberObfuscator.processLuaCodeMixed(result);
                    stats.processedFeatures.push('Number Obfuscation (Mixed Methods)');
                } else {
                    result = this.numberObfuscator.processLuaCode(result, numberMethod);
                    stats.processedFeatures.push(`Number Obfuscation (${numberMethod})`);
                }
            }

            // Note: Variable and function renaming disabled

            stats.finalSize = result.length;
            stats.compressionRatio = ((stats.finalSize - stats.originalSize) / stats.originalSize * 100).toFixed(2);

        } catch (error) {
            stats.errors.push(error.message);
            console.error('Obfuscation error:', error);
        }

        return {
            code: result,
            stats: stats
        };
    }

    // Quick obfuscation with default settings
    quickObfuscate(code) {
        if (this.useProfessional) {
            return this.professionalObfuscator.quickObfuscate(code);
        }
        return this.obfuscate(code, {
            stringEncoding: true,
            stringMethod: 'hex',
            numberObfuscation: true,
            numberMethod: 'arithmetic'
        });
    }

    // Heavy obfuscation with all features enabled
    heavyObfuscate(code) {
        if (this.useProfessional) {
            return this.professionalObfuscator.heavyObfuscate(code);
        }
        return this.obfuscate(code, {
            stringEncoding: true,
            stringMethod: 'base64',
            numberObfuscation: true,
            mixedNumberMethods: true
        });
    }

    // Light obfuscation with minimal changes
    lightObfuscate(code) {
        if (this.useProfessional) {
            return this.professionalObfuscator.lightObfuscate(code);
        }
        return this.obfuscate(code, {
            stringEncoding: true,
            stringMethod: 'reverse',
            numberObfuscation: true,
            numberMethod: 'hex'
        });
    }

    // Get available encoding methods
    getAvailableMethods() {
        if (this.useProfessional) {
            return this.professionalObfuscator.getAvailableMethods();
        }
        return {
            stringMethods: ['hex', 'base64', 'unicode', 'reverse'],
            numberMethods: ['arithmetic', 'bitwise', 'hex', 'expression', 'table', 'function'],
            presets: ['quick', 'heavy', 'light', 'custom'],
            engine: 'Custom JavaScript',
            note: 'Variable and function renaming disabled'
        };
    }

    // Validate Lua code syntax (basic check)
    validateLuaCode(code) {
        if (this.useProfessional) {
            return this.professionalObfuscator.validateLuaCode(code);
        }

        const errors = [];

        // Check for balanced parentheses
        let parenCount = 0;
        let braceCount = 0;
        let bracketCount = 0;

        for (let i = 0; i < code.length; i++) {
            const char = code[i];
            switch (char) {
                case '(':
                    parenCount++;
                    break;
                case ')':
                    parenCount--;
                    break;
                case '{':
                    braceCount++;
                    break;
                case '}':
                    braceCount--;
                    break;
                case '[':
                    bracketCount++;
                    break;
                case ']':
                    bracketCount--;
                    break;
            }
        }

        if (parenCount !== 0) errors.push('Unbalanced parentheses');
        if (braceCount !== 0) errors.push('Unbalanced braces');
        if (bracketCount !== 0) errors.push('Unbalanced brackets');

        // Check for common syntax errors
        const errorPatterns = [
            { pattern: /\bfunction\s*\(/g, message: 'Invalid function syntax' },
            { pattern: /\bend\s*\(/g, message: 'Invalid end statement' }
        ];

        errorPatterns.forEach(({ pattern, message }) => {
            if (pattern.test(code)) {
                errors.push(message);
            }
        });

        return {
            isValid: errors.length === 0,
            errors: errors
        };
    }
}

module.exports = LuaObfuscator;
