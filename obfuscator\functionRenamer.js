class FunctionRenamer {
    constructor() {
        this.reservedWords = new Set([
            'and', 'break', 'do', 'else', 'elseif', 'end', 'false', 'for',
            'function', 'if', 'in', 'local', 'nil', 'not', 'or', 'repeat',
            'return', 'then', 'true', 'until', 'while', 'goto'
        ]);
        
        this.builtinFunctions = new Set([
            'print', 'pairs', 'ipairs', 'next', 'type', 'getmetatable', 'setmetatable',
            'rawget', 'rawset', 'rawequal', 'rawlen', 'tostring', 'tonumber',
            'pcall', 'xpcall', 'error', 'assert', 'select', 'unpack', 'table',
            'string', 'math', 'io', 'os', 'debug', 'coroutine', 'package',
            'require', 'load', 'loadfile', 'loadstring', 'dofile'
        ]);

        // Roblox-specific services and functions to preserve
        this.robloxServices = new Set([
            'game', 'workspace', 'script', 'Players', 'ReplicatedStorage', 'ServerStorage',
            'StarterGui', 'StarterPack', 'StarterPlayer', 'Lighting', 'SoundService',
            'TweenService', 'UserInputService', 'ContextActionService', 'GuiService',
            'TextService', 'MarketplaceService', 'BadgeService', 'GamePassService',
            'DataStoreService', 'MessagingService', 'TeleportService', 'HttpService',
            'RunService', 'Debris', 'SocialService', 'PolicyService', 'LocalizationService'
        ]);
        
        this.functionMap = new Map();
        this.usedNames = new Set();
    }

    // Generate random function names
    generateRandomName(length = 10) {
        const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        const numbers = '0123456789';
        let result = '';
        
        // First character must be a letter
        result += chars.charAt(Math.floor(Math.random() * chars.length));
        
        // Rest can be letters or numbers
        const allChars = chars + numbers;
        for (let i = 1; i < length; i++) {
            result += allChars.charAt(Math.floor(Math.random() * allChars.length));
        }
        
        return result;
    }

    // Generate obfuscated function names
    generateObfuscatedName(original) {
        if (this.functionMap.has(original)) {
            return this.functionMap.get(original);
        }

        let newName;
        do {
            newName = this.generateRandomName();
        } while (
            this.usedNames.has(newName) || 
            this.reservedWords.has(newName) || 
            this.builtinFunctions.has(newName) ||
            this.robloxServices.has(newName)
        );

        this.functionMap.set(original, newName);
        this.usedNames.add(newName);
        return newName;
    }

    // Find all function declarations and calls
    findFunctions(code) {
        const functions = new Set();
        
        // Match function declarations: function functionName() or local function functionName()
        const functionDeclRegex = /(?:local\s+)?function\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\(/g;
        let match;
        
        while ((match = functionDeclRegex.exec(code)) !== null) {
            const funcName = match[1];
            if (!this.reservedWords.has(funcName) && 
                !this.builtinFunctions.has(funcName) && 
                !this.robloxServices.has(funcName)) {
                functions.add(funcName);
            }
        }

        // Match function assignments: functionName = function()
        const functionAssignRegex = /([a-zA-Z_][a-zA-Z0-9_]*)\s*=\s*function\s*\(/g;
        while ((match = functionAssignRegex.exec(code)) !== null) {
            const funcName = match[1];
            if (!this.reservedWords.has(funcName) && 
                !this.builtinFunctions.has(funcName) && 
                !this.robloxServices.has(funcName)) {
                functions.add(funcName);
            }
        }

        // Match local function assignments: local functionName = function()
        const localFunctionAssignRegex = /local\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*=\s*function\s*\(/g;
        while ((match = localFunctionAssignRegex.exec(code)) !== null) {
            const funcName = match[1];
            if (!this.reservedWords.has(funcName) && 
                !this.builtinFunctions.has(funcName) && 
                !this.robloxServices.has(funcName)) {
                functions.add(funcName);
            }
        }

        // Match table method definitions: table.functionName = function() or table:functionName()
        const tableMethodRegex = /([a-zA-Z_][a-zA-Z0-9_]*)[.:]([a-zA-Z_][a-zA-Z0-9_]*)\s*(?:=\s*function\s*\(|\()/g;
        while ((match = tableMethodRegex.exec(code)) !== null) {
            const tableName = match[1];
            const methodName = match[2];

            // Don't rename built-in Lua library methods
            const builtinLibraries = new Set(['string', 'table', 'math', 'io', 'os', 'debug', 'coroutine', 'package']);

            // Don't rename common Roblox methods and properties
            const commonRobloxMethods = new Set([
                'GetService', 'FindFirstChild', 'WaitForChild', 'Clone', 'Destroy',
                'Connect', 'Disconnect', 'Fire', 'InvokeServer', 'InvokeClient',
                'FireServer', 'FireClient', 'FireAllClients', 'Name', 'Parent',
                'Position', 'Size', 'Rotation', 'CFrame', 'Vector3', 'Color3',
                'UDim2', 'BrickColor', 'Material', 'Transparency', 'CanCollide',
                'char', 'sub', 'find', 'gsub', 'match', 'gmatch', 'len', 'lower', 'upper',
                'reverse', 'rep', 'format', 'byte', 'insert', 'remove', 'concat', 'sort'
            ]);

            if (!this.reservedWords.has(methodName) &&
                !this.builtinFunctions.has(methodName) &&
                !this.robloxServices.has(methodName) &&
                !commonRobloxMethods.has(methodName) &&
                !builtinLibraries.has(tableName)) {
                functions.add(methodName);
            }
        }

        return Array.from(functions);
    }

    // Replace function names in code
    replaceFunctions(code, functions) {
        let result = code;
        
        // Sort functions by length (longest first) to avoid partial replacements
        const sortedFuncs = functions.sort((a, b) => b.length - a.length);
        
        sortedFuncs.forEach(funcName => {
            const newName = this.generateObfuscatedName(funcName);
            
            // Replace function declarations
            const declRegex = new RegExp(`((?:local\\s+)?function\\s+)${funcName.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}(\\s*\\()`, 'g');
            result = result.replace(declRegex, `$1${newName}$2`);
            
            // Replace function assignments
            const assignRegex = new RegExp(`(^|[^a-zA-Z0-9_])${funcName.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}(\\s*=\\s*function\\s*\\()`, 'gm');
            result = result.replace(assignRegex, `$1${newName}$2`);
            
            // Replace local function assignments
            const localAssignRegex = new RegExp(`(local\\s+)${funcName.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}(\\s*=\\s*function\\s*\\()`, 'g');
            result = result.replace(localAssignRegex, `$1${newName}$2`);
            
            // Replace function calls (be careful not to replace method calls on Roblox objects)
            const callRegex = new RegExp(`\\b${funcName.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b(?=\\s*\\()`, 'g');
            result = result.replace(callRegex, (match, offset) => {
                // Check if this is a method call (preceded by . or :)
                const beforeMatch = result.substring(Math.max(0, offset - 10), offset);
                if (beforeMatch.match(/[.:]$/)) {
                    return match; // Don't replace method calls
                }
                return newName;
            });
            
            // Replace table method definitions (only the method name part)
            const tableMethodRegex = new RegExp(`([a-zA-Z_][a-zA-Z0-9_]*[.:])${funcName.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}(\\s*(?:=\\s*function\\s*\\(|\\())`, 'g');
            result = result.replace(tableMethodRegex, `$1${newName}$2`);
        });
        
        return result;
    }

    // Main function to rename functions
    renameFunctions(code) {
        this.functionMap.clear();
        this.usedNames.clear();
        
        const functions = this.findFunctions(code);
        return this.replaceFunctions(code, functions);
    }

    // Get the function mapping for debugging
    getFunctionMap() {
        return Object.fromEntries(this.functionMap);
    }

    // Reset the renamer state
    reset() {
        this.functionMap.clear();
        this.usedNames.clear();
    }
}

module.exports = FunctionRenamer;
