const express = require('express');
const router = express.Router();
const LuaObfuscator = require('../obfuscator/luaObfuscator');

const obfuscator = new LuaObfuscator();

// Middleware for request validation
const validateObfuscationRequest = (req, res, next) => {
    const { code } = req.body;
    
    if (!code || typeof code !== 'string') {
        return res.status(400).json({
            error: 'Invalid request',
            message: 'Code parameter is required and must be a string'
        });
    }
    
    if (code.length > 1000000) { // 1MB limit
        return res.status(400).json({
            error: 'Code too large',
            message: 'Code must be less than 1MB'
        });
    }
    
    next();
};

// GET /api/methods - Get available obfuscation methods
router.get('/methods', (req, res) => {
    try {
        const methods = obfuscator.getAvailableMethods();
        res.json({
            success: true,
            data: methods
        });
    } catch (error) {
        res.status(500).json({
            error: 'Internal server error',
            message: error.message
        });
    }
});

// POST /api/obfuscate - Custom obfuscation with options
router.post('/obfuscate', validateObfuscationRequest, (req, res) => {
    try {
        const { code, options = {} } = req.body;
        
        // Validate Lua code
        const validation = obfuscator.validateLuaCode(code);
        if (!validation.isValid) {
            return res.status(400).json({
                error: 'Invalid Lua code',
                message: 'Code contains syntax errors',
                details: validation.errors
            });
        }
        
        const result = obfuscator.obfuscate(code, options);
        
        res.json({
            success: true,
            data: {
                obfuscatedCode: result.code,
                stats: result.stats,
                timestamp: new Date().toISOString()
            }
        });
        
    } catch (error) {
        console.error('Obfuscation error:', error);
        res.status(500).json({
            error: 'Obfuscation failed',
            message: error.message
        });
    }
});

// POST /api/obfuscate/quick - Quick obfuscation with default settings
router.post('/obfuscate/quick', validateObfuscationRequest, (req, res) => {
    try {
        const { code } = req.body;
        
        const validation = obfuscator.validateLuaCode(code);
        if (!validation.isValid) {
            return res.status(400).json({
                error: 'Invalid Lua code',
                message: 'Code contains syntax errors',
                details: validation.errors
            });
        }
        
        const result = obfuscator.quickObfuscate(code);
        
        res.json({
            success: true,
            data: {
                obfuscatedCode: result.code,
                stats: result.stats,
                preset: 'quick',
                timestamp: new Date().toISOString()
            }
        });
        
    } catch (error) {
        console.error('Quick obfuscation error:', error);
        res.status(500).json({
            error: 'Quick obfuscation failed',
            message: error.message
        });
    }
});

// POST /api/obfuscate/heavy - Heavy obfuscation with all features
router.post('/obfuscate/heavy', validateObfuscationRequest, (req, res) => {
    try {
        const { code } = req.body;
        
        const validation = obfuscator.validateLuaCode(code);
        if (!validation.isValid) {
            return res.status(400).json({
                error: 'Invalid Lua code',
                message: 'Code contains syntax errors',
                details: validation.errors
            });
        }
        
        const result = obfuscator.heavyObfuscate(code);
        
        res.json({
            success: true,
            data: {
                obfuscatedCode: result.code,
                stats: result.stats,
                preset: 'heavy',
                timestamp: new Date().toISOString()
            }
        });
        
    } catch (error) {
        console.error('Heavy obfuscation error:', error);
        res.status(500).json({
            error: 'Heavy obfuscation failed',
            message: error.message
        });
    }
});

// POST /api/obfuscate/light - Light obfuscation with minimal changes
router.post('/obfuscate/light', validateObfuscationRequest, (req, res) => {
    try {
        const { code } = req.body;
        
        const validation = obfuscator.validateLuaCode(code);
        if (!validation.isValid) {
            return res.status(400).json({
                error: 'Invalid Lua code',
                message: 'Code contains syntax errors',
                details: validation.errors
            });
        }
        
        const result = obfuscator.lightObfuscate(code);
        
        res.json({
            success: true,
            data: {
                obfuscatedCode: result.code,
                stats: result.stats,
                preset: 'light',
                timestamp: new Date().toISOString()
            }
        });
        
    } catch (error) {
        console.error('Light obfuscation error:', error);
        res.status(500).json({
            error: 'Light obfuscation failed',
            message: error.message
        });
    }
});

// POST /api/validate - Validate Lua code syntax
router.post('/validate', (req, res) => {
    try {
        const { code } = req.body;
        
        if (!code || typeof code !== 'string') {
            return res.status(400).json({
                error: 'Invalid request',
                message: 'Code parameter is required and must be a string'
            });
        }
        
        const validation = obfuscator.validateLuaCode(code);
        
        res.json({
            success: true,
            data: {
                isValid: validation.isValid,
                errors: validation.errors,
                codeLength: code.length
            }
        });
        
    } catch (error) {
        console.error('Validation error:', error);
        res.status(500).json({
            error: 'Validation failed',
            message: error.message
        });
    }
});

// GET /api/health - Health check endpoint
router.get('/health', (req, res) => {
    res.json({
        success: true,
        message: 'Lua Obfuscator API is running',
        timestamp: new Date().toISOString(),
        version: '1.0.0'
    });
});

module.exports = router;
