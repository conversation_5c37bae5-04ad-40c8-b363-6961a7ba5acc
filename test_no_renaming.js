const LuaObfuscator = require('./obfuscator/luaObfuscator');

const testCode = `-- Test Script
local Players = game:GetService("Players")
local player = Players.LocalPlayer

local function greetPlayer(playerName)
    print("Hello " .. playerName .. "!")
    local health = 100
    local damage = 25
    return health - damage
end

local function calculateScore(baseScore, multiplier)
    return baseScore * multiplier
end

-- Main execution
local myPlayer = player.Name
local result = greetPlayer(myPlayer)
local finalScore = calculateScore(result, 2)

print("Final score: " .. finalScore)`;

console.log('🧪 Testing Obfuscator WITHOUT Variable/Function Renaming\n');
console.log('Original Code:');
console.log('='.repeat(60));
console.log(testCode);
console.log('='.repeat(60));
console.log('');

const obfuscator = new LuaObfuscator();

// Test Quick Obfuscation
console.log('📦 Quick Obfuscation (Professional):');
console.log('-'.repeat(40));
try {
    const quickResult = obfuscator.quickObfuscate(testCode);
    console.log('✅ Success!');
    console.log('Features:', quickResult.stats.processedFeatures.join(', '));
    console.log('Size change:', quickResult.stats.compressionRatio + '%');
    console.log('Engine:', quickResult.stats.engine || 'Professional lua-format');
    console.log('');
    console.log('Obfuscated Code:');
    console.log('-'.repeat(40));
    console.log(quickResult.code);
    console.log('-'.repeat(40));
    console.log('');
} catch (error) {
    console.log('❌ Error:', error.message);
}

// Test Available Methods
console.log('🛠️ Available Methods:');
console.log('-'.repeat(40));
const methods = obfuscator.getAvailableMethods();
console.log('Engine:', methods.engine);
console.log('String Methods:', methods.stringMethods.join(', '));
console.log('Features:', methods.features ? methods.features.join(', ') : 'N/A');
console.log('Note:', methods.note);
console.log('');

console.log('✅ Variable and function names are preserved!');
console.log('🎯 Only string encoding and math optimization applied.');
console.log('🎉 Test Complete!');
