<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lua Obfuscator - Roblox Script Protection</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/theme/monokai.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <h1><i class="fas fa-shield-alt"></i> Lua Obfuscator</h1>
                <p>Professional Roblox Script Protection Tool</p>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Control Panel -->
            <div class="control-panel">
                <div class="preset-buttons">
                    <button class="btn btn-primary" onclick="obfuscateCode('quick')">
                        <i class="fas fa-bolt"></i> Quick Obfuscate
                    </button>
                    <button class="btn btn-secondary" onclick="obfuscateCode('heavy')">
                        <i class="fas fa-lock"></i> Heavy Obfuscate
                    </button>
                    <button class="btn btn-tertiary" onclick="obfuscateCode('light')">
                        <i class="fas fa-feather"></i> Light Obfuscate
                    </button>
                    <button class="btn btn-custom" onclick="toggleCustomOptions()">
                        <i class="fas fa-cog"></i> Custom Options
                    </button>
                </div>

                <!-- Custom Options Panel -->
                <div id="customOptions" class="custom-options" style="display: none;">
                    <div class="options-grid">
                        <div class="option-group">
                            <label>
                                <input type="checkbox" id="stringEncoding" checked>
                                <span>String Encoding</span>
                            </label>
                            <select id="stringMethod">
                                <option value="hex">Hexadecimal</option>
                                <option value="base64">Base64</option>
                                <option value="unicode">Unicode</option>
                                <option value="reverse">Reverse</option>
                                <option value="caesar">Caesar Cipher</option>
                                <option value="xor">XOR Encoding</option>
                            </select>
                        </div>

                        <div class="option-group">
                            <label style="opacity: 0.6; cursor: not-allowed;">
                                <input type="checkbox" id="variableRenaming" disabled>
                                <span>Variable Renaming (Disabled)</span>
                            </label>
                        </div>

                        <div class="option-group">
                            <label style="opacity: 0.6; cursor: not-allowed;">
                                <input type="checkbox" id="functionRenaming" disabled>
                                <span>Function Renaming (Disabled)</span>
                            </label>
                        </div>

                        <div class="option-group">
                            <label>
                                <input type="checkbox" id="numberObfuscation" checked>
                                <span>Number Obfuscation</span>
                            </label>
                            <select id="numberMethod">
                                <option value="arithmetic">Arithmetic</option>
                                <option value="bitwise">Bitwise</option>
                                <option value="hex">Hexadecimal</option>
                                <option value="expression">Expression</option>
                                <option value="table">Table-based</option>
                                <option value="function">Function-based</option>
                            </select>
                        </div>

                        <div class="option-group">
                            <label>
                                <input type="checkbox" id="mixedNumberMethods">
                                <span>Mixed Number Methods</span>
                            </label>
                        </div>
                    </div>

                    <button class="btn btn-primary" onclick="obfuscateCode('custom')">
                        <i class="fas fa-play"></i> Apply Custom Obfuscation
                    </button>
                </div>
            </div>

            <!-- Code Editors -->
            <div class="editor-container">
                <div class="editor-panel">
                    <div class="editor-header">
                        <h3><i class="fas fa-code"></i> Original Lua Code</h3>
                        <div class="editor-actions">
                            <button class="btn-small" onclick="clearInput()">
                                <i class="fas fa-trash"></i> Clear
                            </button>
                            <button class="btn-small" onclick="loadExample()">
                                <i class="fas fa-file-code"></i> Load Example
                            </button>
                        </div>
                    </div>
                    <textarea id="inputCode" placeholder="-- Paste your Lua/Roblox script here...
local function greetPlayer(player)
    print('Hello, ' .. player.Name .. '!')
    return true
end

local players = game:GetService('Players')
local localPlayer = players.LocalPlayer

greetPlayer(localPlayer)"></textarea>
                </div>

                <div class="editor-panel">
                    <div class="editor-header">
                        <h3><i class="fas fa-shield-alt"></i> Obfuscated Code</h3>
                        <div class="editor-actions">
                            <button class="btn-small" onclick="copyOutput()">
                                <i class="fas fa-copy"></i> Copy
                            </button>
                            <button class="btn-small" onclick="downloadOutput()">
                                <i class="fas fa-download"></i> Download
                            </button>
                        </div>
                    </div>
                    <textarea id="outputCode" placeholder="Obfuscated code will appear here..." readonly></textarea>
                </div>
            </div>

            <!-- Stats Panel -->
            <div id="statsPanel" class="stats-panel" style="display: none;">
                <h3><i class="fas fa-chart-bar"></i> Obfuscation Statistics</h3>
                <div class="stats-grid">
                    <div class="stat-item">
                        <span class="stat-label">Original Size:</span>
                        <span id="originalSize" class="stat-value">-</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Final Size:</span>
                        <span id="finalSize" class="stat-value">-</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Size Change:</span>
                        <span id="sizeChange" class="stat-value">-</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Features Applied:</span>
                        <span id="featuresApplied" class="stat-value">-</span>
                    </div>
                </div>
                <div id="variableMap" class="variable-map" style="display: none;">
                    <h4>Renaming Mapping:</h4>
                    <div id="variableMapContent"></div>
                </div>
            </div>

            <!-- Loading Indicator -->
            <div id="loadingIndicator" class="loading-indicator" style="display: none;">
                <div class="spinner"></div>
                <p>Obfuscating your code...</p>
            </div>

            <!-- Error Display -->
            <div id="errorDisplay" class="error-display" style="display: none;">
                <div class="error-content">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span id="errorMessage"></span>
                </div>
            </div>
        </main>

        <!-- Footer -->
        <footer class="footer">
            <p>&copy; 2024 Lua Obfuscator. Professional script protection for Roblox developers.</p>
            <div class="footer-links">
                <a href="#" onclick="showInfo()">About</a>
                <a href="#" onclick="showHelp()">Help</a>
                <a href="/api/health" target="_blank">API Status</a>
            </div>
        </footer>
    </div>

    <!-- Scripts -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/lua/lua.min.js"></script>
    <script src="script.js"></script>
</body>
</html>
