class StringEncoder {
    constructor() {
        this.encodingMethods = {
            base64: this.base64Encode.bind(this),
            hex: this.hexEncode.bind(this),
            unicode: this.unicodeEncode.bind(this),
            reverse: this.reverseEncode.bind(this),
            caesar: this.caesarEncode.bind(this),
            xor: this.xorEncode.bind(this)
        };
    }

    // Base64 encoding for Lua
    base64Encode(str) {
        const encoded = Buffer.from(str).toString('base64');
        return `(function() local b='ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/' local function decode(data) local result = '' for i = 1, #data do local c = string.sub(data, i, i) local pos = string.find(b, c) if pos then result = result .. string.char(pos - 1) end end return result end return decode('${encoded}') end)()`;
    }

    // Hexadecimal encoding
    hexEncode(str) {
        const encoded = Buffer.from(str).toString('hex');
        return `(function() local hex = '${encoded}' local result = '' for i = 1, #hex, 2 do result = result .. string.char(tonumber(string.sub(hex, i, i + 1), 16)) end return result end)()`;
    }

    // Unicode encoding
    unicodeEncode(str) {
        const encoded = str.split('').map(char => char.charCodeAt(0)).join(',');
        return `(function() local codes = {${encoded}} local result = '' for i = 1, #codes do result = result .. string.char(codes[i]) end return result end)()`;
    }

    // Reverse encoding
    reverseEncode(str) {
        const reversed = str.split('').reverse().join('');
        return `(function() local rev = '${reversed}' return string.reverse(rev) end)()`;
    }

    // Caesar cipher encoding
    caesarEncode(str, shift = 3) {
        const encoded = str.split('').map(char => {
            const code = char.charCodeAt(0);
            return String.fromCharCode((code + shift) % 256);
        }).join('');

        return `(function() local encoded = '${encoded}' local shift = ${shift} local result = '' for i = 1, #encoded do local code = string.byte(encoded, i) result = result .. string.char((code - shift) % 256) end return result end)()`;
    }

    // XOR encoding
    xorEncode(str, key = 42) {
        const encoded = str.split('').map(char => char.charCodeAt(0) ^ key).join(',');
        return `(function() local encoded = {${encoded}} local key = ${key} local result = '' for i = 1, #encoded do result = result .. string.char(encoded[i] ~ key) end return result end)()`;
    }

    // Main encoding function
    encodeString(str, method = 'hex') {
        if (!this.encodingMethods[method]) {
            throw new Error(`Unknown encoding method: ${method}`);
        }
        return this.encodingMethods[method](str);
    }

    // Process Lua code to encode all strings
    processLuaCode(code, method = 'hex') {
        // Match string literals (both single and double quotes)
        const stringRegex = /(['"])((?:\\.|(?!\1)[^\\])*?)\1/g;

        return code.replace(stringRegex, (match, quote, content) => {
            // Skip if it's already encoded or contains special patterns
            if (content.includes('function()') || content.includes('return') || content.length < 2) {
                return match;
            }

            try {
                return this.encodeString(content, method);
            } catch (error) {
                console.warn(`Failed to encode string: ${content}`, error);
                return match;
            }
        });
    }
}

module.exports = StringEncoder;
