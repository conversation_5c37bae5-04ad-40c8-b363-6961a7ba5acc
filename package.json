{"name": "lua-obfuscator", "version": "1.0.0", "description": "Professional Lua obfuscator for Roblox scripts using lua-format (luamin.js) and custom obfuscation techniques", "main": "server.js", "scripts": {"start": "node server.js", "dev": "node server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["lua", "obfuscator", "roblo<PERSON>", "script", "protection", "encoding", "lua-format", "luamin", "professional"], "author": "Lua Obfuscator Team", "license": "MIT", "type": "commonjs", "dependencies": {"body-parser": "^2.2.0", "cors": "^2.8.5", "express": "^5.1.0", "lua-format": "^1.5.2", "multer": "^2.0.1"}}